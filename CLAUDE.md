# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🎯 Project Overview

A WeChat public account article scraper/spider system built for scraping articles from Chinese WeChat public accounts. Uses both HTTP requests and Selenium WebDriver for content extraction.

## 🏗️ Architecture

The codebase consists of two main Python files:

- **wechatspider_fixed.py**: Core WeChatSpider class with all scraping logic
- **wechatspider_functions.py**: Convenience wrapper functions providing easy-to-use APIs

### Key Components

1. **WeChatSpider Class** (`wechatspider_fixed.py:21`)
   - Handles authentication via TOKEN, COOKIE, and fakeid pairs
   - Provides both requests-based and Selenium-based scraping
   - Manages data storage with automatic file organization

2. **Convenience Functions** (`wechatspider_functions.py`)
   - Wrapper functions for common scraping tasks
   - Single-account and multi-account operations
   - Time-based filtering and keyword-based extraction

## 📁 File Structure

```
wechat_spider/
├── wechatspider_fixed.py       # Core scraping class
├── wechatspider_functions.py   # High-level API functions  
├── data/                       # Auto-created output directory
│   ├── [account_name]/         # One folder per WeChat account
│   └── mixed/                  # Multi-account results
└── README.md
```

## 🔧 Configuration Requirements

Before scraping, configure these values in `wechatspider_fixed.py:24-32`:

### Required Parameters
- `FAKEIDS`: Mapping of account names to WeChat fakeids
- `TOKEN`: WeChat API token
- `COOKIE`: Full authentication cookie string

### Optional Configuration
- `USER_AGENT_LIST`: Browser user agents for rotation
- ChromeDriver path: Auto-configured for Windows (`D:\各种installer\chormDriver\chromedriver-win64\chromedriver-win64\chromedriver.exe`)

## 🚀 Getting Started

### Basic Scraping
```python
from wechatspider_functions import *

# Download latest article from specific account
article = download_latest("公众号名称")

# Download recent week's articles
articles = download_recent_week("公众号名称", max_articles=10)

# Download from all configured accounts
all_latest = download_all_latest()
```

### Configuration Steps
1. Identify target WeChat account fakeids
2. Capture authentication token and cookie from browser
3. Update configuration parameters in `wechatspider_fixed.py`

## 📝 Output Formats

Files are automatically saved to `./data/[account_name]/` or `./data/mixed/` with timestamps:

- **JSON**: Complete article data with content and metadata
- **Excel**: Structured table format with key fields
- **CSV**: Simple title-link format (basic compatibility)

### Key Data Fields
- Title, author, publication time, content, images, word count
- WeChat account name, article URL, crawl timestamp

## ⚡ Development Commands

```bash
# Run core spider directly
python wechatspider_fixed.py

# Run convenience functions
python wechatspider_functions.py

# Test specific function
python -c "from wechatspider_functions import download_latest; print(download_latest('account_name'))"
```

## 📊 Supported Operations

### Single Account
- `download_latest()`: One latest article
- `download_recent_week/month/quarter()`: Time-based filtering  
- `download_by_keyword()`: Keyword-based filtering
- `get_article_summary()`: Metadata without full download

### Multi Account
- `download_all_latest()`: Latest from all configured accounts
- `download_all_recent_week/month()`: Batch time-based operations
- Search across all accounts with keyword filtering

## 🔍 Key Dependencies

Main imports across files:
- `requests`: HTTP client for API calls
- `selenium`: Browser automation (optional)
- `beautifulsoup4`: HTML parsing
- `pandas`: Data export/formatting
- Local ChromeDriver at specified path

## ⚠️ Important Notes

- Authentication tokens expire frequently - requires manual renewal
- Built-in rate limiting to prevent blocks
- First confirm configuration validity with `get_article_summary()`
- Output directories auto-created on first run