#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多账号微信公众号文章爬取工具
引用 wechatspider_fixed.py 和 wechatspider_functions.py
实现多账号近30天公众号文章爬取
"""

# 引用现有的爬虫模块
from wechatspider_fixed import WeChatSpider
from wechatspider_functions import download_all_recent_month

def main():
    """
    使用现有函数爬取多账号近30天公众号文章
    """
    print("🚀 开始爬取多账号近30天公众号文章")
    print("="*50)

    # 显示配置的公众号
    spider = WeChatSpider()
    print(f"📋 配置的公众号: {list(spider.FAKEIDS.keys())}")
    print(f"📅 爬取时间范围: 近30天")
    print(f"📄 每个公众号最大文章数: 50篇")
    print("="*50)

    try:
        # 直接调用现有的多账号近一个月爬取函数
        results = download_all_recent_month(
            max_articles_per_account=50,  # 每个公众号最多50篇文章
            use_selenium=False            # 使用requests方法，更稳定
        )

        # 显示结果统计
        if results:
            total_articles = sum(len(articles) for articles in results.values())
            print(f"\n🎉 爬取完成！")
            print(f"📊 总计爬取 {total_articles} 篇文章")
            print(f"📱 涉及 {len(results)} 个公众号")

            print(f"\n📋 各公众号详细统计:")
            for account_name, articles in results.items():
                if articles:
                    print(f"   • {account_name}: {len(articles)}篇文章")
                else:
                    print(f"   • {account_name}: 0篇文章 ❌")

            print(f"\n💾 数据已保存到 ./data/ 目录")
            print(f"   - 单个公众号数据: ./data/{{公众号名称}}/")
            print(f"   - 汇总数据: ./data/mixed/")

        else:
            print("⚠️ 未获取到任何文章数据")

    except Exception as e:
        print(f"❌ 爬取过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()