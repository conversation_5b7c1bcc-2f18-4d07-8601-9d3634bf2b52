#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多账号微信公众号文章爬取工具
基于 wechatspider_fixed.py 和 wechatspider_functions.py
实现多账号近30天公众号文章的批量爬取功能
"""

import time
import datetime
from typing import Dict, List
from wechatspider_fixed import WeChatSpider

def crawl_all_accounts_recent_30days(
    max_articles_per_account: int = 50,
    use_selenium: bool = False,
    save_formats: List[str] = ['json', 'excel'],
    custom_days: int = 30
) -> Dict[str, List[dict]]:
    """
    爬取所有公众号近30天的文章

    Args:
        max_articles_per_account (int): 每个公众号最大文章数量，默认50篇
        use_selenium (bool): 是否使用Selenium，默认False（推荐使用requests）
        save_formats (list): 保存格式列表，可选 ['json', 'excel', 'csv']
        custom_days (int): 自定义天数，默认30天

    Returns:
        Dict[str, List[dict]]: {公众号名称: [文章列表]}
    """
    print("🚀 开始执行多账号近30天公众号文章爬取任务")
    print("="*60)

    # 初始化爬虫
    spider = WeChatSpider()

    # 显示配置信息
    print(f"📋 爬取配置:")
    print(f"   • 目标公众号: {list(spider.FAKEIDS.keys())}")
    print(f"   • 时间范围: 近{custom_days}天")
    print(f"   • 每账号最大文章数: {max_articles_per_account}篇")
    print(f"   • 使用Selenium: {'是' if use_selenium else '否'}")
    print(f"   • 保存格式: {', '.join(save_formats)}")
    print("="*60)

    results = {}
    total_articles = 0
    start_time = datetime.datetime.now()

    try:
        account_count = len(spider.FAKEIDS)
        current_account = 0

        for account_name in spider.FAKEIDS.keys():
            current_account += 1
            print(f"\n📱 [{current_account}/{account_count}] 正在处理公众号: {account_name}")
            print("-" * 50)

            account_start_time = datetime.datetime.now()

            try:
                # 爬取该账号的文章
                articles = spider.crawl_recent_articles(
                    days_back=custom_days,
                    max_articles=max_articles_per_account,
                    use_selenium=use_selenium,
                    account_name=account_name
                )

                results[account_name] = articles
                account_article_count = len(articles)
                total_articles += account_article_count

                # 计算用时
                account_duration = datetime.datetime.now() - account_start_time

                if articles:
                    print(f"✅ {account_name} 爬取成功!")
                    print(f"   📊 获取文章数: {account_article_count}篇")
                    print(f"   ⏱️  用时: {account_duration.total_seconds():.1f}秒")

                    # 保存单个账号的数据
                    _save_account_data(spider, articles, account_name, save_formats)
                else:
                    print(f"⚠️  {account_name} 未获取到文章")

                # 重置spider的articles避免数据混淆
                spider.articles = []

            except Exception as e:
                print(f"❌ 处理公众号 {account_name} 时出错: {e}")
                results[account_name] = []
                continue

            # 账号间延迟，避免被封
            if current_account < account_count:
                print("⏳ 等待处理下一个公众号...")
                time.sleep(20)  # 20秒延迟

        # 保存汇总数据
        if total_articles > 0:
            _save_summary_data(results, save_formats, custom_days)

        # 显示最终统计
        show_crawl_summary(results, start_time, custom_days)

        return results

    except Exception as e:
        print(f"❌ 爬取过程中发生错误: {e}")
        return results

    finally:
        spider.close_driver()

def _save_account_data(spider: WeChatSpider, articles: List[dict], account_name: str, save_formats: List[str]):
    """保存单个账号的数据"""
    if not articles:
        return

    spider.articles = articles

    try:
        if 'json' in save_formats:
            spider.save_to_json(account_name=account_name)
        if 'excel' in save_formats:
            spider.save_to_excel(account_name=account_name)
        if 'csv' in save_formats:
            spider.save_to_csv(account_name=account_name)
    except Exception as e:
        print(f"⚠️  保存 {account_name} 数据时出错: {e}")

def _save_summary_data(results: Dict[str, List[dict]], save_formats: List[str], days: int):
    """保存汇总数据到mixed文件夹"""
    print(f"\n💾 正在保存汇总数据...")

    # 合并所有文章
    all_articles = []
    for _, articles in results.items():
        all_articles.extend(articles)

    if not all_articles:
        print("⚠️  没有文章数据可保存")
        return

    # 创建临时spider用于保存
    temp_spider = WeChatSpider()
    temp_spider.articles = all_articles

    try:
        if 'json' in save_formats:
            filename = temp_spider.save_to_json()
            if filename:
                print(f"📄 汇总JSON已保存: {filename}")

        if 'excel' in save_formats:
            filename = temp_spider.save_to_excel()
            if filename:
                print(f"📊 汇总Excel已保存: {filename}")

        if 'csv' in save_formats:
            filename = temp_spider.save_to_csv()
            if filename:
                print(f"📋 汇总CSV已保存: {filename}")

    except Exception as e:
        print(f"❌ 保存汇总数据时出错: {e}")

def show_crawl_summary(results: Dict[str, List[dict]], start_time: datetime.datetime, days: int):
    """显示爬取结果统计"""
    print("\n" + "="*60)
    print("📊 爬取任务完成统计")
    print("="*60)

    total_articles = sum(len(articles) for articles in results.values())
    total_duration = datetime.datetime.now() - start_time

    print(f"⏱️  总用时: {total_duration.total_seconds():.1f}秒 ({total_duration})")
    print(f"📱 处理公众号数: {len(results)}")
    print(f"📄 总文章数: {total_articles}篇")
    print(f"📅 时间范围: 近{days}天")

    print(f"\n📋 各公众号详细统计:")
    for account_name, articles in results.items():
        article_count = len(articles)
        if article_count > 0:
            latest_article = max(articles, key=lambda x: x['pub_time'])
            print(f"   • {account_name}: {article_count}篇 (最新: {latest_article['title'][:30]}...)")
        else:
            print(f"   • {account_name}: 0篇 ❌")

    if total_articles > 0:
        avg_per_account = total_articles / len(results)
        print(f"\n📈 平均每账号: {avg_per_account:.1f}篇")
        print(f"💾 数据已保存到 ./data/ 目录")

    print("="*60)

def crawl_specific_accounts(
    account_names: List[str],
    days_back: int = 30,
    max_articles_per_account: int = 50,
    use_selenium: bool = False
) -> Dict[str, List[dict]]:
    """
    爬取指定公众号的近期文章

    Args:
        account_names (List[str]): 指定的公众号名称列表
        days_back (int): 向前追溯天数
        max_articles_per_account (int): 每个公众号最大文章数
        use_selenium (bool): 是否使用Selenium

    Returns:
        Dict[str, List[dict]]: {公众号名称: [文章列表]}
    """
    print(f"🚀 开始爬取指定公众号近{days_back}天文章")
    print(f"📋 目标公众号: {account_names}")

    spider = WeChatSpider()
    results = {}

    try:
        # 验证公众号名称
        valid_accounts = []
        for name in account_names:
            if name in spider.FAKEIDS:
                valid_accounts.append(name)
            else:
                print(f"⚠️  公众号 '{name}' 不在配置中，跳过")

        if not valid_accounts:
            print("❌ 没有有效的公众号名称")
            return {}

        for account_name in valid_accounts:
            print(f"\n📱 正在处理: {account_name}")

            articles = spider.crawl_recent_articles(
                days_back=days_back,
                max_articles=max_articles_per_account,
                use_selenium=use_selenium,
                account_name=account_name
            )

            results[account_name] = articles
            print(f"✅ {account_name}: {len(articles)}篇文章")

            spider.articles = []  # 重置

        return results

    except Exception as e:
        print(f"❌ 爬取过程出错: {e}")
        return results
    finally:
        spider.close_driver()

def quick_crawl_30days():
    """快速执行30天爬取任务的便捷函数"""
    print("🚀 执行快速30天爬取任务...")
    return crawl_all_accounts_recent_30days(
        max_articles_per_account=50,
        use_selenium=False,
        save_formats=['json', 'excel']
    )

def main():
    """主执行函数"""
    print("🎯 多账号微信公众号文章爬取工具")
    print("基于 wechatspider_fixed.py 和 wechatspider_functions.py")
    print("="*60)

    # 显示菜单
    print("请选择执行模式:")
    print("1. 爬取所有公众号近30天文章 (推荐)")
    print("2. 爬取所有公众号近7天文章")
    print("3. 爬取所有公众号近90天文章")
    print("4. 自定义爬取参数")
    print("5. 快速测试 (每账号最多5篇)")

    try:
        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == '1':
            print("\n🚀 开始执行30天爬取任务...")
            results = crawl_all_accounts_recent_30days()

        elif choice == '2':
            print("\n🚀 开始执行7天爬取任务...")
            results = crawl_all_accounts_recent_30days(
                max_articles_per_account=20,
                custom_days=7
            )

        elif choice == '3':
            print("\n🚀 开始执行90天爬取任务...")
            results = crawl_all_accounts_recent_30days(
                max_articles_per_account=100,
                custom_days=90
            )

        elif choice == '4':
            print("\n📝 自定义参数设置:")
            days = int(input("请输入天数 (默认30): ") or "30")
            max_articles = int(input("请输入每账号最大文章数 (默认50): ") or "50")
            use_selenium = input("是否使用Selenium? (y/N): ").lower().startswith('y')

            results = crawl_all_accounts_recent_30days(
                max_articles_per_account=max_articles,
                use_selenium=use_selenium,
                custom_days=days
            )

        elif choice == '5':
            print("\n🧪 开始快速测试...")
            results = crawl_all_accounts_recent_30days(
                max_articles_per_account=5,
                custom_days=7
            )

        else:
            print("❌ 无效选择，退出程序")
            return

        # 显示结果
        if results:
            total = sum(len(articles) for articles in results.values())
            print(f"\n🎉 任务完成！共爬取 {total} 篇文章")
        else:
            print("\n⚠️  未获取到任何文章")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()