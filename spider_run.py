#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多账号微信公众号文章爬取工具
引用 wechatspider_fixed.py 和 wechatspider_functions.py
实现多账号近30天公众号文章爬取
"""

# 引用现有的爬虫模块
from wechatspider_fixed import WeChatSpider
import time
import random

def setup_multi_user_sessions(spider):
    """设置多用户会话"""
    print("\n🔧 多用户会话配置")
    print("="*40)

    # 显示当前会话数
    print(f"📊 当前会话数: {len(spider.USER_SESSIONS)}")

    # 询问是否添加第二个用户
    add_user = input("是否添加第二个用户的Cookie和Token? (y/N): ").lower().startswith('y')

    if add_user:
        print("\n请输入第二个用户的信息:")
        user_name = input("用户名称 (默认: 用户2): ").strip() or "用户2"
        token = input("Token: ").strip()
        cookie = input("Cookie: ").strip()

        if token and cookie:
            spider.add_user_session(user_name, token, cookie)
            print(f"✅ 成功添加用户: {user_name}")
        else:
            print("❌ Token或Cookie为空，跳过添加")

    print(f"🎯 最终会话配置: {len(spider.USER_SESSIONS)} 个用户会话")
    for i, session in enumerate(spider.USER_SESSIONS):
        print(f"   {i+1}. {session['name']}")
    print("="*40)

def crawl_with_multi_user_sessions(spider, max_articles_per_account=50):
    """
    使用多用户会话进行爬取，自动轮换用户避免频率控制
    """
    print(f"\n🚀 开始多用户会话爬取，共{len(spider.USER_SESSIONS)}个会话")

    results = {}

    try:
        for account_name in spider.FAKEIDS.keys():
            print(f"\n📱 正在处理公众号: {account_name}")
            print("-" * 40)

            # 显示当前使用的会话
            session_info = spider.get_current_session_info()
            print(f"🔑 当前会话: {session_info['name']} (使用次数: {session_info['usage_count']})")

            # 爬取该账号的文章
            articles = spider.crawl_recent_articles(
                days_back=30,
                max_articles=max_articles_per_account,
                use_selenium=False,
                account_name=account_name
            )

            results[account_name] = articles

            if articles:
                print(f"✅ {account_name}: 成功获取 {len(articles)} 篇文章")
                # 保存单个账号数据
                spider.save_to_json(account_name=account_name)
                spider.save_to_excel(account_name=account_name)
            else:
                print(f"⚠️ {account_name}: 未获取到文章")

            # 重置articles避免混淆
            spider.articles = []

            # 如果有多个会话，为下一个账号切换会话
            if len(spider.USER_SESSIONS) > 1:
                old_session = spider.get_current_session_info()
                spider.switch_session()  # 自动切换到使用次数最少的会话
                new_session = spider.get_current_session_info()
                if old_session['index'] != new_session['index']:
                    print(f"🔄 已切换会话: {old_session['name']} → {new_session['name']}")

            # 账号间延迟
            delay = random.randint(30, 60)  # 多用户时可以减少延迟
            print(f"⏳ 账号间延迟 {delay} 秒...")
            time.sleep(delay)

        return results

    except Exception as e:
        print(f"❌ 多用户会话爬取出错: {e}")
        return results

    finally:
        spider.close_driver()

def main():
    """
    使用现有函数爬取多账号近30天公众号文章
    """
    print("🚀 开始爬取多账号近30天公众号文章")
    print("="*50)

    # 显示配置的公众号
    spider = WeChatSpider()
    print(f"📋 配置的公众号: {list(spider.FAKEIDS.keys())}")
    print(f"📅 爬取时间范围: 近30天")
    print(f"📄 每个公众号最大文章数: 50篇")
    print("\n🛡️ 防频率控制和网络优化措施已启用:")
    print("   • 智能重试机制 (最多3次)")
    print("   • 增强请求头伪装")
    print("   • 多用户会话轮换")
    print("   • 连接池优化 (连接池: 20, 最大连接: 50)")
    print("   • 网络重试策略 (5次重试, 指数退避)")
    print("   • 超时优化 (连接: 15s, 读取: 45s)")
    print("   • 账号间延迟: 60-120秒")
    print("   • 文章间延迟: 20-35秒")
    print("   • 页面间延迟: 5-15秒")
    print("="*50)

    # 设置多用户会话
    setup_multi_user_sessions(spider)

    try:
        # 创建一个增强版的爬取函数，支持多用户会话
        results = crawl_with_multi_user_sessions(spider, max_articles_per_account=50)

        # 显示结果统计
        if results:
            total_articles = sum(len(articles) for articles in results.values())
            print(f"\n🎉 爬取完成！")
            print(f"📊 总计爬取 {total_articles} 篇文章")
            print(f"📱 涉及 {len(results)} 个公众号")

            print(f"\n📋 各公众号详细统计:")
            for account_name, articles in results.items():
                if articles:
                    print(f"   • {account_name}: {len(articles)}篇文章")
                else:
                    print(f"   • {account_name}: 0篇文章 ❌")

            print(f"\n💾 数据已保存到 ./data/ 目录")
            print(f"   - 单个公众号数据: ./data/{{公众号名称}}/")
            print(f"   - 汇总数据: ./data/mixed/")

        else:
            print("⚠️ 未获取到任何文章数据")

    except Exception as e:
        print(f"❌ 爬取过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()