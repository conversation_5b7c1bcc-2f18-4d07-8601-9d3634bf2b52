#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号爬虫 - 基于用户修正后的link_spider重新合并
完全保持用户原始代码的逻辑和配置
"""

import os
import requests
import datetime
import json
import time
import math
import random
import pandas as pd
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

class WeChatSpider:
    def __init__(self):
        # ===【配置项 - 基于用户修正后的代码】===
        self.FAKEIDS = {
            "南京市中级人民法院": "MzA5OTIwMDMyNg==",
            "南京检察": "MzA4NDIwNDMwMA==",
        }

        # 添加频率控制计数器
        self.freq_control_count = 0
        
        # 使用用户修正后的配置
        self.TOKEN = "1684462343"
        self.COOKIE = "appmsglist_action_3897341759=card; eas_sid=81Q7l3u093u754X9I6k7j3u7u4; LW_uid=01s7i3V1E450p1s025M9F8Z8w8; RK=0xV9ZGcHML; ptcz=affa4548bbec598730eb5b4627ff92a465b30bcccbd8c94963e2270bceca6671; LW_sid=W1m7l311w4e0h1E0L8P8o2t102; yyb_muid=2C54FADA2A7263653920EFF22BA06237; _qimei_uuid42=1970316021f100c6f4c520e4dec0b6c7702dd21e8a; _qimei_fingerprint=1cfee87b0447c63c2d698e5f3e88ec6c; _qimei_q36=; _qimei_h38=8f95278df4c520e4dec0b6c70200000a619703; ua_id=iQnKDTsfTJUs2ockAAAAALSLP3IZgrW15F1cZgaWzU0=; _clck=fd3rp5|1|fxm|0; wxuin=52543012121396; mm_lang=zh_CN; uuid=40a8c71446bcdd0d422dc1a960b2f210; rand_info=CAESIMj0/+hEs96rvC7FM34yVpeFD20wAhyRDcpQj+wTQV96; slave_bizuin=3897341759; data_bizuin=3897341759; bizuin=3897341759; data_ticket=ch+DxBAcCNxBncCibFIx7lSPJbHuEnDGnr/BolQg7xOQdlCfiTkPK4Y9NMis7fSk; slave_sid=dGpQTkZVcTJRdzBGVFFTX3NDb2ZzTFJNOWNCeTUyeHdreXNDdDFqdXoyOEFnUDNpdFp1UXdDNXJQNG9xTHpjdXdzSHdFY2x4dmdhZUJEWjN1c2dLZmp1aWhjY1NkYXo2bmx0RXpmak9mY1NScXh1VG9pandHZjVFajBCakJtd3NsVWJUSWNMZjF5b1VnQW5q; slave_user=gh_dcaf14b275a0; xid=03b789b0c9c6a1276fbeede4e9ea6d9d; _clsk=1tv9wvs|1752550539979|36|1|mp.weixin.qq.com/weheat-agent/payload/record"
        
        # 用户的User-Agent列表
        self.USER_AGENT_LIST = [
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.85 Safari/537.36 115Browser/6.0.3',
            'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_8; en-us) AppleWebKit/534.50 (KHTML, like Gecko) Version/5.1 Safari/534.50',
            'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-us) AppleWebKit/534.50 (KHTML, like Gecko) Version/5.1 Safari/534.50',
            'Mozilla/5.0 (Windows NT 6.1; rv:2.0.1) Gecko/******** Firefox/4.0.1',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_0) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11',
            'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0',
            'Mozilla/5.0 (Windows NT 6.1; rv:2.0.1) Gecko/******** Firefox/4.0.1',
            "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.75 Mobile Safari/537.36",
        ]
        
        # 创建数据文件夹结构
        os.makedirs("./data", exist_ok=True)
        for account_name in self.FAKEIDS.keys():
            os.makedirs(f"./data/{account_name}", exist_ok=True)
        os.makedirs("./data/mixed", exist_ok=True)
        
        # 存储文章数据
        self.articles = []
        self.driver = None
    
    def fetch_article_links(self, begin=0, count=5, account_name=None):
        """获取文章链接列表 - 使用用户修正后的API和参数"""
        # 确定使用哪个公众号
        if account_name and account_name in self.FAKEIDS:
            target_fakeid = self.FAKEIDS[account_name]
            target_name = account_name
        else:
            target_name = list(self.FAKEIDS.keys())[0]
            target_fakeid = self.FAKEIDS[target_name]
        
        # 使用用户修正后的URL和参数结构
        url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
        
        data = {
            "token": self.TOKEN,
            "lang": "zh_CN",
            "f": "json",
            "ajax": "1",
            "action": "list_ex",
            "begin": str(begin),
            "count": str(count),
            "query": "",
            "fakeid": target_fakeid,
            "type": "9",
        }
        
        # 随机选择User-Agent并增强请求头
        user_agent = random.choice(self.USER_AGENT_LIST)
        headers = {
            "Cookie": self.COOKIE,
            "User-Agent": user_agent,
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://mp.weixin.qq.com/",
            "X-Requested-With": "XMLHttpRequest",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        
        # 智能重试机制
        max_retries = 3
        base_delay = 30  # 基础延迟30秒

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    delay = base_delay * (2 ** (attempt - 1))  # 指数退避：30s, 60s, 120s
                    print(f"⏳ 第{attempt + 1}次尝试，等待{delay}秒后重试...")
                    time.sleep(delay)

                print(f"📡 请求文章链接: {target_name} (尝试 {attempt + 1}/{max_retries})")
                response = requests.get(url, headers=headers, params=data, timeout=30)

                if response.status_code != 200:
                    print(f"⚠️ HTTP状态码异常: {response.status_code}")
                    if attempt == max_retries - 1:
                        return []
                    continue

                content_json = response.json()

                # 检查频率控制错误
                if "base_resp" in content_json:
                    base_resp = content_json["base_resp"]
                    if base_resp.get("err_msg") == "freq control":
                        self.freq_control_count += 1
                        print(f"⚠️ 遇到频率控制限制 (ret: {base_resp.get('ret')}) - 第{self.freq_control_count}次")

                        if self.freq_control_count >= 3:
                            print("🚨 频繁遇到频率控制，建议检查以下几点：")
                            print("   1. Cookie是否已过期？")
                            print("   2. Token是否需要更新？")
                            print("   3. 是否在高峰时段访问？")
                            print("   4. 建议暂停30分钟后再试")

                        if attempt == max_retries - 1:
                            print("❌ 达到最大重试次数，建议稍后再试或检查Cookie是否过期")
                            return []
                        continue

                # 检查返回结果
                if "app_msg_list" not in content_json:
                    print(f"⚠️ 返回数据格式异常: {content_json}")
                    if attempt == max_retries - 1:
                        return []
                    continue

                # 成功获取数据，处理结果
                results = []
                for item in content_json["app_msg_list"]:
                    title = item.get("title", "")
                    link = item.get("link", "")
                    create_time = item.get("create_time", 0)

                    # 转换时间格式
                    t = time.localtime(create_time)
                    pub_time = time.strftime("%Y-%m-%d %H:%M:%S", t)

                    results.append({
                        "title": title,
                        "url": link,
                        "pub_time": pub_time,
                        "account_name": target_name
                    })

                print(f"✅ 获取到 {len(results)} 篇文章链接")
                return results

            except Exception as e:
                print(f"❌ 请求异常: {e}")
                if attempt == max_retries - 1:
                    print("❌ 达到最大重试次数，获取文章链接失败")
                    return []
                continue

        # 如果所有重试都失败了
        return []
    
    def fetch_article_content_requests(self, article):
        """使用requests获取文章内容"""
        try:
            print(f"📄 抓取内容: {article['title']}")
            
            # 使用随机User-Agent
            user_agent = random.choice(self.USER_AGENT_LIST)
            headers = {"User-Agent": user_agent}
            
            res = requests.get(article["url"], headers=headers, timeout=15)
            
            if res.status_code != 200:
                print(f"⚠️ 内容请求失败: {res.status_code}")
                return None
                
            soup = BeautifulSoup(res.text, "html.parser")
            
            # 获取文章内容
            content_elem = soup.select_one("div.rich_media_content")
            if not content_elem:
                content_elem = soup.select_one("#js_content")
            content = content_elem.get_text(separator="\n", strip=True) if content_elem else ""
            
            # 获取文章标题
            title_elem = soup.select_one("h1.rich_media_title")
            title = title_elem.get_text(strip=True) if title_elem else article["title"]
            
            # 获取作者信息
            author_elem = soup.select_one("span.rich_media_meta_text")
            author = author_elem.get_text(strip=True) if author_elem else article.get("account_name", "未知公众号")
            
            # 获取图片链接
            images = []
            if content_elem:
                img_elements = content_elem.find_all('img')
                for img in img_elements:
                    src = img.get('data-src') or img.get('src')
                    if src and src.startswith('http'):
                        images.append(src)
            
            return {
                "title": title,
                "author": author,
                "account_name": article.get("account_name", "未知公众号"),
                "pub_time": article["pub_time"],
                "url": article["url"],
                "content": content,
                "images": images,
                "word_count": len(content),
                "crawl_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"❌ 获取内容失败: {e}")
            return None
    
    def setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--lang=zh-CN")
            
            # 尝试使用具体的ChromeDriver路径
            chrome_driver_path = r"D:\各种installer\chormDriver\chromedriver-win64\chromedriver-win64\chromedriver.exe"
            
            if os.path.exists(chrome_driver_path):
                print(f"使用本地ChromeDriver: {chrome_driver_path}")
                service = Service(executable_path=chrome_driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                print("本地ChromeDriver不存在，尝试自动下载...")
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            print("✅ Selenium WebDriver 初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Selenium WebDriver 初始化失败: {e}")
            return False
    
    def fetch_article_content_selenium(self, article):
        """使用Selenium获取文章内容"""
        try:
            if not self.driver:
                if not self.setup_selenium_driver():
                    return None
            
            self.driver.get(article["url"])
            time.sleep(2)  # 防止加载不完全
            
            soup = BeautifulSoup(self.driver.page_source, "html.parser")
            div = soup.find("div", class_="rich_media_content")
            content = div.get_text(separator="\n", strip=True) if div else ""
            
            # 获取标题
            title_elem = soup.select_one("h1.rich_media_title")
            title = title_elem.get_text(strip=True) if title_elem else article["title"]
            
            # 获取作者
            author_elem = soup.select_one("span.rich_media_meta_text")
            author = author_elem.get_text(strip=True) if author_elem else article.get("account_name", "未知公众号")
            
            # 获取图片
            images = []
            if div:
                img_elements = div.find_all('img')
                for img in img_elements:
                    src = img.get('data-src') or img.get('src')
                    if src and src.startswith('http'):
                        images.append(src)
            
            return {
                "title": title,
                "author": author,
                "account_name": article.get("account_name", "未知公众号"),
                "pub_time": article["pub_time"],
                "url": article["url"],
                "content": content,
                "images": images,
                "word_count": len(content),
                "crawl_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"❌ Selenium获取内容失败: {e}")
            return None
    
    def crawl_latest_article(self, use_selenium=False, account_name=None):
        """爬取最新的一篇文章"""
        target_account = account_name or list(self.FAKEIDS.keys())[0]
        print(f"🚀 开始爬取 {target_account} 最新文章...")
        
        # 获取文章链接
        links = self.fetch_article_links(begin=0, count=1, account_name=target_account)
        
        if not links:
            print("❌ 未获取到文章链接")
            return None
        
        latest_article = links[0]
        print(f"📖 找到最新文章: {latest_article['title']}")
        
        # 添加随机延迟避免被封
        time.sleep(random.randint(2, 5))
        
        # 获取文章详细内容
        if use_selenium:
            detail = self.fetch_article_content_selenium(latest_article)
            if detail is None:
                print("⚠️ Selenium方法失败，尝试requests方法...")
                detail = self.fetch_article_content_requests(latest_article)
        else:
            detail = self.fetch_article_content_requests(latest_article)
        
        if detail:
            self.articles = [detail]
            print(f"✅ 成功爬取文章: {detail['title']}")
            print(f"📱 公众号: {detail['account_name']}")
            print(f"📝 文章字数: {detail['word_count']} 字")
            
            # 保存文件
            self.save_to_json(account_name=target_account)
            self.save_to_excel(account_name=target_account)
            
            return detail
        else:
            print("❌ 文章内容获取失败")
            return None
    
    def crawl_recent_articles(self, days_back=30, max_articles=10, use_selenium=False, account_name=None):
        """爬取最近一段时间的文章"""
        target_account = account_name or list(self.FAKEIDS.keys())[0]
        print(f"🚀 开始爬取 {target_account} 最近{days_back}天的文章，最多{max_articles}篇...")
        
        all_articles = []
        now = datetime.datetime.now()
        cutoff_date = now - datetime.timedelta(days=days_back)
        
        page = 0
        total_fetched = 0
        
        while total_fetched < max_articles:
            print(f"📋 正在获取第{page+1}页文章链接...")
            links = self.fetch_article_links(begin=page*5, count=5, account_name=target_account)
            
            if not links:
                print("📄 没有更多文章了")
                break
            
            for article in links:
                if total_fetched >= max_articles:
                    break
                
                try:
                    # 检查文章日期
                    pub_dt = datetime.datetime.strptime(article["pub_time"], "%Y-%m-%d %H:%M:%S")
                    if pub_dt < cutoff_date:
                        print(f"⏰ 文章 '{article['title']}' 超出时间范围，跳过")
                        continue
                    
                    # 获取文章详细内容
                    if use_selenium:
                        detail = self.fetch_article_content_selenium(article)
                        if detail is None:
                            detail = self.fetch_article_content_requests(article)
                    else:
                        detail = self.fetch_article_content_requests(article)
                    
                    if detail:
                        all_articles.append(detail)
                        total_fetched += 1
                        print(f"✅ 已完成 {total_fetched}/{max_articles}")
                        
                        # 添加延迟避免被封（增强版）
                        delay = random.randint(20, 35)
                        print(f"⏳ 文章间延迟 {delay} 秒...")
                        time.sleep(delay)
                    else:
                        print(f"❌ 文章内容获取失败: {article['title']}")
                        
                except Exception as e:
                    print(f"❌ 处理文章时出错: {e}")
                    continue
            
            page += 1
            # 页面间延迟（增强版）
            page_delay = random.randint(5, 15)
            print(f"⏳ 页面间延迟 {page_delay} 秒...")
            time.sleep(page_delay)
        
        self.articles = all_articles
        print(f"🎉 共成功爬取 {len(all_articles)} 篇文章")
        return all_articles
    
    def crawl_all_accounts_latest(self, use_selenium=False):
        """遍历所有公众号，爬取每个公众号的最新文章"""
        print(f"🚀 开始遍历所有公众号爬取最新文章...")
        print(f"📋 将爬取的公众号: {list(self.FAKEIDS.keys())}")
        
        all_articles = []
        
        for account_name in self.FAKEIDS.keys():
            print(f"\n{'='*50}")
            print(f"📱 正在处理公众号: {account_name}")
            
            try:
                latest = self.crawl_latest_article(use_selenium=use_selenium, account_name=account_name)
                if latest:
                    all_articles.append(latest)
                    print(f"✅ {account_name} 最新文章获取成功")
                else:
                    print(f"❌ {account_name} 最新文章获取失败")
                
                # 添加延迟避免被封（增强版 - 账号间延迟）
                account_delay = random.randint(60, 120)  # 增加到60-120秒
                print(f"⏳ 账号间延迟 {account_delay} 秒，避免频率控制...")
                time.sleep(account_delay)
                
            except Exception as e:
                print(f"❌ 处理公众号 {account_name} 时出错: {e}")
                continue
        
        # 保存混合数据
        if all_articles:
            self.articles = all_articles
            print(f"\n🎉 遍历完成！共成功爬取 {len(all_articles)} 个公众号的最新文章")
            self.save_to_json()  # 保存到mixed文件夹
            self.save_to_excel()
        
        return all_articles
    
    def generate_filename(self, base_name, extension, account_name=None):
        """生成带时间戳的文件名"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if account_name and account_name in self.FAKEIDS:
            folder = f"./data/{account_name}"
            filename = f"{base_name}_{timestamp}.{extension}"
        else:
            folder = "./data/mixed"
            filename = f"{base_name}_{timestamp}.{extension}"
        
        return os.path.join(folder, filename)
    
    def save_to_json(self, account_name=None):
        """保存数据到JSON文件"""
        if not self.articles:
            print("⚠️ 没有数据可保存")
            return
        
        base_name = "latest_article" if len(self.articles) == 1 else "recent_articles"
        filename = self.generate_filename(base_name, "json", account_name)
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(self.articles, f, ensure_ascii=False, indent=2)
            print(f"💾 JSON数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存JSON文件失败: {e}")
            return None
    
    def save_to_excel(self, account_name=None):
        """保存数据到Excel文件"""
        if not self.articles:
            print("⚠️ 没有数据可保存")
            return
        
        base_name = "latest_article" if len(self.articles) == 1 else "recent_articles"
        filename = self.generate_filename(base_name, "xlsx", account_name)
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            df_data = []
            for article in self.articles:
                df_data.append({
                    '公众号': article.get('account_name', '未知公众号'),
                    '标题': article['title'],
                    '作者': article['author'],
                    '发布时间': article['pub_time'],
                    '文章链接': article['url'],
                    '字数': article['word_count'],
                    '图片数量': len(article['images']),
                    '正文内容': article['content'],  # 保存完整正文内容
                    '爬取时间': article['crawl_time']
                })
            
            df = pd.DataFrame(df_data)
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"📊 Excel数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存Excel文件失败: {e}")
            return None
    
    def save_to_csv(self, account_name=None):
        """保存数据到CSV文件（兼容用户原始代码的保存格式）"""
        if not self.articles:
            print("⚠️ 没有数据可保存")
            return
        
        base_name = "articles_list"
        filename = self.generate_filename(base_name, "csv", account_name)
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            df_data = []
            for article in self.articles:
                df_data.append({
                    'title': article['title'],
                    'link': article['url'],
                    'create_time': article['pub_time']
                })
            
            df = pd.DataFrame(df_data)
            df.to_csv(filename, mode='a', encoding='utf-8', index=False)
            print(f"📄 CSV数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 保存CSV文件失败: {e}")
            return None
    
    def close_driver(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("🔒 浏览器已关闭")
            except:
                pass

def main():
    """测试主函数"""
    spider = WeChatSpider()
    
    try:
        print("🎯 基于用户修正代码的微信公众号爬虫")
        print(f"📋 支持的公众号: {list(spider.FAKEIDS.keys())}")
        
        # 测试单个公众号
        print("\n" + "="*50)
        print("测试: 爬取威科先行最新文章")
        print("="*50)
        
        latest = spider.crawl_latest_article(use_selenium=False, account_name="威科先行")
        if latest:
            print(f"✅ 测试成功！")
            print(f"📖 标题: {latest['title']}")
            print(f"📝 字数: {latest['word_count']}")
        
        # 测试所有公众号
        spider.articles = []  # 清空数据
        
        print("\n" + "="*50)
        print("测试: 爬取所有公众号最新文章")
        print("="*50)
        
        all_latest = spider.crawl_all_accounts_latest(use_selenium=False)
        if all_latest:
            print(f"✅ 所有公众号测试成功，共获取 {len(all_latest)} 篇文章")
            for article in all_latest:
                print(f"📱 {article['account_name']}: {article['title']} ({article['word_count']} 字)")
        
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        spider.close_driver()

if __name__ == "__main__":
    main() 